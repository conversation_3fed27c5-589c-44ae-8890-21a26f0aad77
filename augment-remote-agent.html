<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用远程代理 - Augment Code</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .image-container {
            margin: 40px 0;
            text-align: center;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .image-container img:hover {
            transform: scale(1.02);
        }

        .image-caption {
            margin-top: 15px;
            color: var(--text-secondary);
            font-size: 1rem;
            font-style: italic;
        }

        .screenshot {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .comparison-table td {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.1rem;
        }

        .comparison-table tr:nth-child(even) {
            background-color: var(--bg-light);
        }

        .check-mark {
            color: var(--google-green);
            font-weight: 900;
            font-size: 1.3rem;
        }

        .partial-check {
            color: var(--google-yellow);
            font-weight: 900;
            font-size: 1.3rem;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
            .comparison-table {
                font-size: 0.9rem;
            }
            .comparison-table th,
            .comparison-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>使用远程代理</h1>
            <div class="author">Augment Code 官方文档</div>
            <div class="date">云端AI编程助手解决方案</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "使用远程代理完成跨工作流程的任务——实现功能、升级依赖项或编写拉取请求——全部在云端完成，并在需要时提供Visual Studio Code的全部功能。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p><span class="emphasis">Augment远程代理</span>是一个强大的工具，可以帮助您在安全的云环境中端到端地完成软件开发任务。您可以在独立任务上并行运行<span class="stat-number">多个代理</span>，并从Visual Studio Code内部监控和管理它们的进度。</p>
            </div>

            <!-- 关于远程代理 -->
            <div class="section">
                <h2>关于远程代理</h2>
                
                <p>远程代理可以在正常模式或自动模式下运行，就像基于IDE的代理一样，并会在需要您注意时通知您。</p>

                <h3>远程代理与普通代理有何不同？</h3>
                <p>远程代理是IDE绑定代理的云版本。每个远程代理都在自己的安全环境中运行，拥有自己的工作空间——这些都为您管理。每个远程代理独立工作并在自己的分支上工作，因此您可以让多个代理同时在同一个存储库上工作。</p>

                <!-- 远程代理架构图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/remote-agent-architecture.png"
                         alt="远程代理架构图"
                         class="screenshot">
                    <div class="image-caption">远程代理架构 - 云端独立环境与本地IDE的协作模式</div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 访问远程代理 -->
            <div class="section">
                <h2>访问远程代理</h2>

                <p>要启动新的远程代理，只需打开Augment面板并从输入框的下拉菜单中选择远程代理。</p>

                <!-- 远程代理选择界面截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/remote-agent-dropdown.png"
                         alt="远程代理下拉菜单选择界面"
                         class="screenshot">
                    <div class="image-caption">在Augment面板的输入框下拉菜单中选择远程代理</div>
                </div>

                <h3>代理仪表板</h3>
                <p>您可以通过点击Augment面板顶部的<span class="highlight">展开仪表板图标</span>在远程代理仪表板中查看所有远程代理。从仪表板中，您可以看到所有代理的状态，通过SSH连接到它们，或在不再需要时删除它们。</p>

                <!-- 代理仪表板截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/agent-dashboard.png"
                         alt="远程代理仪表板界面"
                         class="screenshot">
                    <div class="image-caption">远程代理仪表板 - 查看所有代理状态和管理选项</div>
                </div>
            </div>

            <!-- 使用远程代理 -->
            <div class="section">
                <h2>使用远程代理</h2>
                
                <p>远程代理在您处理任务和项目时的功能几乎与IDE绑定代理相同。由于它们在云中异步运行，您可以在编辑器中处理其他项目时访问和管理它们。</p>

                <!-- 创建远程代理界面截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/create-remote-agent.png"
                         alt="创建远程代理界面"
                         class="screenshot">
                    <div class="image-caption">创建远程代理 - 选择存储库、分支和环境</div>
                </div>

                <!-- 创建远程代理步骤 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">创建远程代理</div>
                        <div class="card-body">
                            <ol>
                                <li><span class="highlight">选择存储库</span>：选择您希望代理工作的存储库</li>
                                <li><span class="highlight">选择分支</span>：选择分支或让代理为您创建新分支</li>
                                <li><span class="highlight">选择环境</span>：选择或创建代理运行的环境</li>
                                <li><span class="highlight">输入提示</span>：使用自然语言输入您的提示并点击创建代理</li>
                            </ol>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">代理环境</div>
                        <div class="card-body">
                            <p>每个远程代理都在云中的安全、独立环境中运行。这使每个代理都有自己的工作空间、存储库副本和虚拟化操作系统来运行其他工具和命令。</p>
                            <p>您可以使用<span class="stat-number">基础环境</span>，或使用bash脚本设置自定义环境来配置代理完成任务所需的工具。</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow);">代理通知</div>
                        <div class="card-body">
                            <p>默认情况下，当代理完成任务或需要您注意时，您将在VS Code中收到通知。</p>
                            <p>您可以通过点击Augment面板线程列表中的<span class="highlight">铃铛图标</span>来禁用远程代理的通知。</p>
                        </div>
                    </div>
                </div>

                <!-- 代理工作界面截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/agent-working.png"
                         alt="远程代理工作界面"
                         class="screenshot">
                    <div class="image-caption">远程代理工作界面 - 实时查看代理执行任务的进度</div>
                </div>

                <h3>与代理迭代</h3>
                <p>代理完成任务后，您可以通过发送额外消息继续与代理迭代。代理将继续处理任务，使用过去的对话作为上下文。如果您需要直接编辑文件，可以通过SSH连接到代理环境。</p>

                <h3>审查更改</h3>
                <p>您可以通过点击操作来展开视图，审查代理所做的每一个更改。查看文件更改的差异、完整的终端命令和输出，以及外部集成调用的结果。</p>

                <!-- 代理更改审查截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/review-changes.png"
                         alt="审查代理更改界面"
                         class="screenshot">
                    <div class="image-caption">审查代理更改 - 查看文件差异和执行历史</div>
                </div>

                <h3>停止或指导代理</h3>
                <p>您可以随时点击停止来中断代理。这将暂停操作，允许您纠正看到代理做错的事情。当代理工作时，您也可以提示代理尝试不同的方法，这将自动停止代理并提示它纠正路线。</p>

                <h3>连接到远程代理环境</h3>
                <p>您需要在Visual Studio Code中安装<span class="highlight">Remote-SSH扩展</span>才能连接到远程代理。如果您没有安装，系统会自动提示您安装。</p>

                <p>有时您可能需要连接到远程代理以直接查看或编辑文件，在这种情况下，您可以通过SSH连接到代理环境。从远程代理仪表板，点击您希望连接的代理卡片中的SSH到代理按钮。</p>

                <!-- SSH连接截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/ssh-connection.png"
                         alt="SSH连接到远程代理"
                         class="screenshot">
                    <div class="image-caption">通过SSH连接到远程代理环境进行直接编辑</div>
                </div>

                <h3>打开拉取请求</h3>
                <p>当代理完成工作后，您可以打开拉取请求，将您的更改提交审查并合并到主分支。从线程列表中选择代理并点击创建PR。代理将创建分支、提交更改并为您打开拉取请求。</p>

                <!-- 创建PR截图 -->
                <div class="image-container">
                    <img src="https://mintlify.s3.us-west-1.amazonaws.com/augment-mtje7p526w/images/remote-agent/create-pr.png"
                         alt="创建拉取请求界面"
                         class="screenshot">
                    <div class="image-caption">创建拉取请求 - 将代理的工作提交到GitHub</div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 功能对比 -->
            <div class="section">
                <h2>功能对比表</h2>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>您想要做什么？</th>
                            <th>聊天</th>
                            <th>代理</th>
                            <th>远程代理</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>询问有关代码的问题</td>
                            <td class="partial-check">☑️</td>
                            <td class="check-mark">✅</td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>获取重构代码的建议</td>
                            <td class="partial-check">☑️</td>
                            <td class="check-mark">✅</td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>为选定的代码行添加新功能</td>
                            <td class="partial-check">☑️</td>
                            <td class="check-mark">✅</td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>添加跨多个文件的新功能</td>
                            <td></td>
                            <td class="check-mark">✅</td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>记录新功能</td>
                            <td></td>
                            <td class="check-mark">✅</td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>在同一存储库中处理多个任务</td>
                            <td></td>
                            <td></td>
                            <td class="check-mark">✅</td>
                        </tr>
                        <tr>
                            <td>关闭VS Code后继续工作</td>
                            <td></td>
                            <td></td>
                            <td class="check-mark">✅</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 使用案例 -->
            <div class="section">
                <h2>使用案例</h2>
                
                <p>使用远程代理处理软件开发工作流程的各个方面，从简单的配置更改到功能实现。远程代理最适合可以独立于其他工作完成的离散任务。</p>

                <ul>
                    <li><span class="highlight">快速编辑</span>：创建拉取请求以调整配置值，如将功能标志从FALSE更改为TRUE</li>
                    <li><span class="highlight">修复小问题</span>：修复代码库中从未进入您TODO列表顶部的小错误或问题</li>
                    <li><span class="highlight">执行重构</span>：在文件之间移动函数，同时保持编码约定并确保无错误操作</li>
                    <li><span class="highlight">探索替代方案</span>：运行多个远程代理为问题创建替代解决方案</li>
                    <li><span class="highlight">新功能的初稿</span>：直接从GitHub Issue或Linear Ticket开始实现全新功能的拉取请求</li>
                    <li><span class="highlight">创建测试覆盖</span>：为您新开发的功能生成单元测试</li>
                    <li><span class="highlight">生成文档</span>：为您的库和功能生成全面的文档</li>
                </ul>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "远程代理支持您的日常工程任务，让您能够专注于最重要的创新工作，而让AI处理重复性和耗时的开发任务。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2024 Augment Code | 远程代理技术文档</p>
        </div>
    </div>
</body>
</html>
