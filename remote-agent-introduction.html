<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>远程智能体：在您规划下一步时清理积压任务</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .video-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .video-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(0,0,0,0.2);
        }

        .video-wrapper {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
        }

        .video-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
            .video-container {
                margin: 0 10px;
                max-width: calc(100% - 20px);
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>远程智能体：在您规划下一步时清理积压任务</h1>
            <div class="author">Guy Gur-Ari</div>
            <div class="date">2025年5月7日 | Augment Code 联合创始人</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "每个工程团队都有一个'我们会处理的'工单墓地：脆弱的测试、过时的文档、小错误，以及那些永远爬不上冲刺墙的恼人'纸割伤'。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p><span class="emphasis">远程智能体</span>让您能够并行化 Augment Agent 来处理小任务，清理积压工作，消除工程繁琐工作。远程智能体在云端运行，随时可用，您可以启动工单，然后回来审查它们的工作再提交。</p>
            </div>

            <!-- 第一部分 -->
            <div class="section">
                <h2>为什么我们构建远程智能体</h2>
                <p>每个工程团队都有一个"我们会处理的"工单墓地：脆弱的测试、过时的文档、小错误，以及那些永远爬不上冲刺墙的恼人"纸割伤"。它们很重要——只是<span class="emphasis">今天</span>不重要。</p>
                
                <p>远程智能体改变了这一切。它们在云端运行，让您能够并行处理这些积压任务，而您可以专注于更重要的工作。</p>
            </div>

            <!-- 远程智能体能做什么 -->
            <div class="section">
                <h2>远程智能体能为您做什么</h2>
                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">消除不稳定测试</div>
                        <div class="card-body">
                            <p>杀死阻塞 CI 运行的不稳定测试，让您的持续集成流程更加可靠。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">清除文档债务</div>
                        <div class="card-body">
                            <p>更新 README、API 示例、迁移指南，让文档保持最新状态。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow); color: var(--text-primary);">无风险重构</div>
                        <div class="card-body">
                            <p>用现代模式包装遗留模块，保持测试绿色通过。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">快速处理错误积压</div>
                        <div class="card-body">
                            <p>比分类会议更快地解决错误积压工单。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">提高覆盖率</div>
                        <div class="card-body">
                            <p>编写单元测试和集成测试，直到阈值达到 <span class="stat-number">≥ 95%</span>。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">批量迁移配置</div>
                        <div class="card-body">
                            <p>对整个仓库进行 lint/格式化，而不会冻结您的 IDE。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 最佳实践 -->
            <div class="section">
                <h2>远程智能体最佳实践</h2>
                <p>使用远程智能体，恭喜您——您现在是技术负责人了。成功管理远程智能体团队的关键是保持任务<span class="highlight">小型化、范围明确、具体且经过验证</span>。</p>
                
                <ol>
                    <li><span class="highlight">把它们想象成"一群热切的实习生"：</span> 分配紧凑、自包含的工单，您可以在一次会议中审查完成。</li>
                    <li><span class="highlight">小目标获胜：</span> <span class="emphasis">"重构 X 并运行测试直到绿色"</span> 比模糊的请求如<span class="emphasis">"让代码更好"</span>表现更佳。</li>
                    <li><span class="highlight">要求自我验证：</span> 包含运行测试套件、linter 或自定义检查的说明——智能体会迭代直到通过。</li>
                    <li><span class="highlight">像不信任它们一样审查拉取请求：</span> 智能体很便宜，但不是万无一失的。</li>
                </ol>
                
                <p>阅读我们的<a href="https://www.augmentcode.com/blog/best-practices-for-using-ai-coding-agents" style="color: var(--google-blue); text-decoration: none; font-weight: 700;">AI 编码智能体使用最佳实践详细指南</a>，获取更多与 Augment Code Agent 良好协作的技巧。</p>
            </div>

            <!-- 视频介绍部分 -->
            <div style="margin: 40px 0; text-align: center;">
                <h3 style="margin-bottom: 30px; color: var(--google-blue);">观看远程智能体介绍视频</h3>
                <div class="video-container">
                    <div class="video-wrapper">
                        <iframe
                            src="https://www.youtube.com/embed/yp262nUnBLU"
                            title="Introducing Remote Agent"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            referrerpolicy="strict-origin-when-cross-origin"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>
                <p style="margin-top: 20px; color: var(--text-secondary); font-size: 1rem; font-style: italic;">
                    深入了解远程智能体如何革命性地改变您的开发工作流程
                </p>
            </div>

            <!-- 图片部分 -->
            <div style="margin: 40px 0; text-align: center;">
                <figure>
                    <img src="https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/681aa71d81fb3dddb9004a16_Blog%20Image%20-%20remote.png" alt="远程智能体工作流程图" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <figcaption style="margin-top: 15px; color: var(--text-secondary); font-size: 1rem;">远程智能体在云端并行处理您的积压任务</figcaption>
                </figure>
            </div>

           
            <!-- 结束引用 -->
            <div class="quote">
                "远程智能体让您能够并行化工作流程，专注于重要的事情，同时让 AI 处理积压的繁琐任务。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | 远程智能体介绍</p>
        </div>
    </div>
</body>
</html>
