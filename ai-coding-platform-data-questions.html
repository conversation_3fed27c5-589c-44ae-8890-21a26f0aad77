<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI编程平台的4个关键数据问题</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .risk-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .risk-table th {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-weight: 700;
            font-size: 1.3rem;
        }

        .risk-table td {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .risk-table tr:last-child td {
            border-bottom: none;
        }

        .risk-zero { color: var(--google-green); font-weight: 700; }
        .risk-low { color: var(--google-yellow); font-weight: 700; }
        .risk-lower { color: #FF9800; font-weight: 700; }
        .risk-high { color: var(--google-red); font-weight: 700; }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
            .risk-table {
                font-size: 0.9rem;
            }
            .risk-table th,
            .risk-table td {
                padding: 15px 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>AI编程平台的4个关键数据问题</h1>
            <div class="author">Scott Dietzen</div>
            <div class="date">Augment Code CEO | 2025年5月6日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "越来越多的开发者正在加入AI的行列——他们能够更快地产出更多更好的代码，同时减少（甚至消除）在冗余和繁琐任务上花费的时间。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p>但是，您的AI助手能够访问的代码也可能被用于训练模型，然后出现在竞争对手的下一个冲刺中。在将AI集成到您的工具链之前，请务必量化与您交出的<span class="emphasis">知识产权</span>相关的风险。</p>
            </div>

            <!-- 风险评估表格 -->
            <div class="section">
                <h2>代码库风险评估</h2>
                <p>许多AI提供商在您的代码上训练模型中看到了巨大价值——不仅是为了改进他们的产品，也是通向人工通用智能（AGI）的路径。当他们的利益与您的利益冲突时，了解您的风险敞口变得至关重要：</p>
                
                <table class="risk-table">
                    <thead>
                        <tr>
                            <th>代码库类型</th>
                            <th>风险级别</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>您拥有的个人项目（不包含您想要保密的想法/代码）和您在工作之外贡献的开源项目</td>
                            <td><span class="risk-zero">零风险</span></td>
                        </tr>
                        <tr>
                            <td>您为公司工作的开源软件</td>
                            <td><span class="risk-low">低风险</span>，但要小心专有代码意外混入。最好检查管理层的整体政策</td>
                        </tr>
                        <tr>
                            <td>不包含组织寻求保护的想法/代码的业务副项目的较小专有代码库，如原型和内部实用程序（无秘密配方）</td>
                            <td><span class="risk-lower">较低风险</span>，但最好与管理层确认以确保安全</td>
                        </tr>
                        <tr>
                            <td>专有代码库（大多数其他软件）</td>
                            <td><span class="risk-high">高风险</span>。您可能需要确保不允许在您的代码上进行训练，并且您的AI提供最先进的安全性和赔偿。确保在启用之前得到管理层对服务条款的批准。</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 企业保护要求 -->
            <div class="section">
                <h2>企业级保护要求</h2>
                <p>在大多数企业中，在组织外部共享机密知识产权（IP）是严重违反公司政策的行为，可能导致<span class="stat-number">职业生涯终结</span>的后果。您需要确保拥有：</p>
                
                <ul>
                    <li><span class="highlight">铁定的合同</span>：禁止为除底层模型的瞬态上下文知识之外的任何目的进行训练和保留</li>
                    <li><span class="highlight">安全加固</span>：通过SOC 2 + ISO 27001审计证明</li>
                    <li><span class="highlight">强有力的IP赔偿</span>：保护免受泄露和污染</li>
                    <li><span class="highlight">所有权证明执行</span>：确保AI不会无意中共享您最安全的代码</li>
                </ul>
            </div>

            <!-- AI供应商关键问题 -->
            <div class="section">
                <h2>AI供应商的关键问题</h2>
                <p>在将任何AI编程助手集成到您的企业环境之前，请询问这些关键问题以保护您组织的知识产权：</p>
                
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">问题1：训练许可</div>
                        <div class="card-body">
                            <p><strong>问题：</strong>您是否曾经或正在未经我明确授权的情况下在我的软件上训练模型？</p>
                            <p><span class="emphasis">Augment Code：否。</span></p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow);">问题2：默认设置</div>
                        <div class="card-body">
                            <p><strong>问题：</strong>默认情况下，是否关闭了对我们专有代码的训练？</p>
                            <p><span class="emphasis">Augment Code：是的。</span></p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">问题3：数据销售</div>
                        <div class="card-body">
                            <p><strong>问题：</strong>您是否曾经或正在将我的数据出售给第三方，让他们在我们的软件上训练您解决方案中未使用的模型？</p>
                            <p><span class="emphasis">Augment Code：否。</span></p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-blue);">问题4：付费保护</div>
                        <div class="card-body">
                            <p><strong>问题：</strong>如果我使用任何付费计划，您是否提供确保我们的软件永远不会被共享或训练的方法，即使我们的工程师错误地选择加入？</p>
                            <p><span class="emphasis">Augment Code：是的。</span></p>
                        </div>
                    </div>
                </div>
                
                <div class="key-point">
                    <p>我们强烈建议在允许任何AI解决方案索引您的代码库之前，以书面形式获得这些答案。如果供应商犹豫不决——或者更糟糕的是，回避其中任何一个问题——请立即离开。</p>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 企业软件价值理解 -->
            <div class="section">
                <h2>理解企业软件价值</h2>
                <p>在评估AI工具时，将您的企业软件分为两类是有帮助的：</p>
                
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">核心代码</div>
                        <div class="card-body">
                            <p>包含对您组织独特、重要价值的子集，通常包括必须保护的商业秘密或专利算法</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-blue);">上下文代码</div>
                        <div class="card-body">
                            <p>支持您核心功能的其他所有内容</p>
                        </div>
                    </div>
                </div>
                
                <p>虽然企业理论上愿意允许在上下文代码上进行训练以换取更好的AI性能，但通常没有实际方法将核心代码与上下文代码分离。这种现实要求在AI集成方面采取谨慎的方法，特别是在企业和政府环境中。</p>
            </div>

            <!-- Augment Code保护方式 -->
            <div class="section">
                <h2>Augment Code如何保护您的知识产权</h2>
                <p>在Augment Code，我们专门为大规模软件工程构建了我们的解决方案。我们的平台为您的整个代码库创建<span class="highlight">实时语义索引</span>，为我们的AI提供最佳上下文，使其能够在您的环境中作为领域专家发挥作用。</p>
                
                <div class="key-point">
                    <p>我们的方法从根本上建立在信任之上。这就是为什么我们：</p>
                </div>
                
                <ul>
                    <li>未经明确许可，绝不在您的软件上进行训练</li>
                    <li>维护<span class="highlight">最先进的安全性</span>来保护您的专有代码</li>
                    <li>确保您的代码永远不会被任何第三方提供商保留</li>
                    <li>提供企业级控制以防止意外数据共享</li>
                </ul>
                
                <p>虽然我们确实为那些希望贡献其代码进行训练的人（通常是开源开发者）提供社区选项，但这是严格的选择加入，并带有明确的好处，如降低成本。即使在这种情况下，我们也绝不会将您的数据出售给任意第三方。</p>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "不要在安全性或生产力上妥协。使用Augment Code，您可以加速开发，同时将知识产权保持在应有的位置——在您的控制之下。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | AI编程平台安全指南</p>
        </div>
    </div>
</body>
</html>
