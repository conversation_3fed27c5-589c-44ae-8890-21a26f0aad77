<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Enhancer 在 Augment Chat 中正式上线</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 400;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Prompt Enhancer 在 Augment Chat 中正式上线</h1>
            <div class="author">Molisha Shah</div>
            <div class="date">GTM 和客户成功负责人 | 2025年5月14日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "半成品的提示词会导致错误的聊天和智能体输出。你知道自己想要什么，但模型不知道。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p><span class="emphasis">Prompt Enhancer</span> 帮助弥合这一差距。它从您的代码库和会话中提取相关上下文，并重写您的提示词，让模型准确理解您的意图。如果 Prompt Enhancer 出现错误，您可以在发送给模型之前就地修正提示词。</p>
            </div>

            <!-- 第一部分 -->
            <div class="section">
                <h2>为什么我们要构建它</h2>
                <p>当我们与社区交流时，一个洞察脱颖而出：<span class="highlight">提示词的清晰度直接决定智能体运行的成功率</span>——无论是生成代码的质量还是实际完成任务的能力。由于AI编程智能体出现还不到一年，每天都有数千名开发者首次接触它们。我们希望每个人都能体验到那种"神奇时刻"——智能体在第一次尝试时就完美完成您的任务。</p>
                
                <p>除了带来愉悦体验，更好的提示词还意味着更少的工具调用和更少的来回指导。这转化为更低的计算成本和更快的周转时间——让您更快地获得结果，成本只是原来的一小部分。</p>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 第二部分 -->
            <div class="section">
                <h2>您可以做什么</h2>
                
                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">减少输入工作量</div>
                        <div class="card-body">
                            <p>输入一个通用提示词，点击 ✨ 增强提示词，获得结构化的详细提示词。</p>
                            <p>从简单的"添加功能"变成包含具体实现细节和上下文的完整提示词。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">自动提取相关上下文</div>
                        <div class="card-body">
                            <p>无需复制粘贴相关代码。Prompt Enhancer 在制作提示词时使用 Augment 的代码库理解能力。</p>
                            <p>智能识别相关文件、函数和模式，自动包含必要的上下文信息。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow);">澄清模型理解</div>
                        <div class="card-body">
                            <p>暴露模型对提示词的理解，在过程中发现其错误——您可以就地编辑它们。</p>
                            <p>透明的提示词差异显示，确保您看到的就是发送给模型的内容。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-blue);">提高答案质量</div>
                        <div class="card-body">
                            <p>更详细、更准确的提示词带来更好的首次响应，减少后续修正。</p>
                            <p>输出与您团队的模式和约束保持一致，减少返工需求。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图片部分 -->
            <div style="margin: 40px 0; text-align: center;">
                <figure>
                    <img src="https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/681aa75e988bd74f929d5ebc_Screenshot%202025-05-05%20at%202.46.56%E2%80%AFPM.png" alt="原始提示词示例" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); margin-bottom: 20px;">
                    <figcaption style="margin-top: 15px; color: var(--text-secondary); font-size: 1rem;">原始简单提示词</figcaption>
                </figure>
                
                <figure>
                    <img src="https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/681aa77f7f9f9e7e4bcc3b8d_CleanShot%202025-05-05%20at%2013.31.39%402x.png" alt="增强后的提示词示例" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <figcaption style="margin-top: 15px; color: var(--text-secondary); font-size: 1rem;">增强后的结构化提示词</figcaption>
                </figure>
            </div>

            <!-- 第三部分 -->
            <div class="section">
                <h2>如何使用</h2>
                <ol>
                    <li><span class="highlight">在聊天或智能体中输入粗略的提示词</span></li>
                    <li><span class="highlight">点击 ✨ 按钮</span></li>
                    <li><span class="highlight">查看增强的提示词，根据需要进行编辑</span></li>
                    <li><span class="highlight">将其发送给模型</span></li>
                </ol>
            </div>

            <!-- 第四部分 -->
            <div class="section">
                <h2>为什么重要</h2>
                <ul>
                    <li><span class="highlight">更好的首次响应</span> – 更有可能实现正确的代码</li>
                    <li><span class="highlight">减少返工</span> – 输出与您团队的模式和约束保持一致</li>
                    <li><span class="highlight">保持工作流</span> – 无需上下文切换，减少输入工作</li>
                </ul>
            </div>

            <!-- 第五部分 -->
            <div class="section">
                <h2>重要信息和后续计划</h2>
                <div class="key-point">
                    <p>现已在 <span class="emphasis">VS Code 和 JetBrains</span> 中提供</p>
                </div>
                <p>提示词差异是透明的——您看到的就是发送给模型的内容。</p>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "更清晰的提示词 = 更好的结果 = 更高的开发效率"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | Prompt Enhancer 功能介绍</p>
        </div>
    </div>
</body>
</html>
